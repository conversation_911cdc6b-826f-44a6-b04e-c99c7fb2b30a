#!/usr/bin/env python3
"""
评估预测器质量的综合脚本

通过多个指标评估三个优化后的预测器：
1. 剪枝质量：重要权重保留率
2. 分布特性：动态范围、熵、方差
3. 层级适应性：不同层类型的表现差异
4. 计算效率：运行时间和内存使用
"""

import torch
import numpy as np
import time
import matplotlib.pyplot as plt
from lib.predictors import PredictorRegistry

def create_realistic_data(batch_size=32, seq_len=128, in_features=512, out_features=256):
    """创建更真实的测试数据，模拟实际神经网络的激活模式"""
    
    # 1. 生成具有不同重要性的权重矩阵
    weight_matrix = torch.randn(out_features, in_features) * 0.1
    
    # 添加一些"重要"的权重（更大的值）
    important_out = torch.randperm(out_features)[:out_features//4]
    important_in = torch.randperm(in_features)[:in_features//4]
    weight_matrix[important_out[:, None], important_in] *= 3.0
    
    # 2. 生成具有真实特征的激活数据
    activations = torch.randn(in_features, seq_len * batch_size) * 0.5
    
    # 模拟注意力模式：一些通道有更高的激活
    attention_channels = torch.randperm(in_features)[:in_features//3]
    activations[attention_channels] *= 2.0
    
    # 模拟稀疏激活：一些通道经常为0
    sparse_channels = torch.randperm(in_features)[:in_features//5]
    activations[sparse_channels] *= (torch.rand(len(sparse_channels), seq_len * batch_size) > 0.7).float()
    
    # 模拟ReLU激活：非负
    relu_channels = torch.randperm(in_features)[:in_features//2]
    activations[relu_channels] = torch.clamp(activations[relu_channels], min=0)
    
    return weight_matrix, activations, important_out, important_in

def compute_comprehensive_stats(activations):
    """计算全面的激活统计信息"""
    stats = {}
    
    # 基础统计
    stats['mean'] = torch.mean(activations, dim=1)
    stats['var'] = torch.var(activations, dim=1, unbiased=False)
    stats['l2_norm'] = torch.norm(activations, p=2, dim=1)
    stats['max_val'] = torch.max(activations, dim=1)[0]
    stats['min_val'] = torch.min(activations, dim=1)[0]
    
    # 高阶统计
    centered = activations - stats['mean'].unsqueeze(1)
    stats['skewness'] = torch.mean(centered ** 3, dim=1) / (stats['var'] ** 1.5 + 1e-8)
    
    # 改进的熵估计
    vars = stats['var']
    ranges = torch.clamp(stats['max_val'] - stats['min_val'], min=1e-8)
    variance_entropy = 0.5 * torch.log(2 * 3.14159 * torch.e * (vars + 1e-12))
    range_entropy = torch.log(ranges + 1e-6)
    stats['entropy'] = 0.7 * variance_entropy + 0.3 * range_entropy
    stats['entropy'] = torch.clamp(stats['entropy'], min=0.0)
    
    return stats

def evaluate_pruning_quality(importance_scores, true_important_weights, sparsity_levels=[0.1, 0.3, 0.5, 0.7, 0.9]):
    """评估剪枝质量：重要权重的保留率"""
    results = {}
    
    scores_flat = importance_scores.flatten()
    true_important_flat = true_important_weights.flatten()
    
    for sparsity in sparsity_levels:
        # 计算剪枝阈值
        threshold = torch.quantile(scores_flat, sparsity)
        
        # 确定被保留的权重
        preserved_mask = scores_flat > threshold
        
        # 计算重要权重的保留率
        important_preserved = (preserved_mask & true_important_flat).sum().float()
        total_important = true_important_flat.sum().float()
        preservation_rate = important_preserved / (total_important + 1e-8)
        
        # 计算精确率和召回率
        total_preserved = preserved_mask.sum().float()
        precision = important_preserved / (total_preserved + 1e-8)
        recall = preservation_rate
        f1_score = 2 * precision * recall / (precision + recall + 1e-8)
        
        results[sparsity] = {
            'preservation_rate': preservation_rate.item(),
            'precision': precision.item(),
            'recall': recall.item(),
            'f1_score': f1_score.item()
        }
    
    return results

def comprehensive_evaluation():
    """综合评估三个预测器"""
    print("综合预测器质量评估")
    print("="*60)
    
    # 生成测试数据
    weight_matrix, activations, important_out, important_in = create_realistic_data()
    activation_stats = compute_comprehensive_stats(activations)
    
    # 创建真实重要权重掩码
    true_important = torch.zeros_like(weight_matrix, dtype=torch.bool)
    true_important[important_out[:, None], important_in] = True
    
    # 测试不同层类型
    layer_types = [
        ('q_proj', 'Query投影'),
        ('gate_proj', '门控投影'),
        ('down_proj', '下投影')
    ]
    
    predictors = {
        'Dolphin': PredictorRegistry.dolphin_predictor,
        'Wanda': PredictorRegistry.wanda_predictor,
        'SNIP': PredictorRegistry.snip_predictor
    }
    
    all_results = {}
    
    for layer_name, layer_desc in layer_types:
        print(f"\n{layer_desc} ({layer_name}) 评估:")
        print("-" * 40)
        
        layer_info = {
            'layer_id': 10,
            'layer_name': layer_name,
            'shape': weight_matrix.shape
        }
        
        layer_results = {}
        
        for pred_name, predictor in predictors.items():
            # 计算重要性分数
            start_time = time.time()
            importance_scores = predictor(activation_stats, weight_matrix, layer_info)
            compute_time = time.time() - start_time
            
            # 评估剪枝质量
            pruning_quality = evaluate_pruning_quality(importance_scores, true_important)
            
            # 计算分布特性
            scores_flat = importance_scores.flatten()
            distribution_stats = {
                'mean': torch.mean(scores_flat).item(),
                'std': torch.std(scores_flat).item(),
                'dynamic_range': (torch.max(scores_flat) - torch.min(scores_flat)).item(),
                'entropy_approx': -torch.sum(torch.softmax(scores_flat, dim=0) * 
                                           torch.log_softmax(scores_flat, dim=0)).item(),
                'sparsity_ratio': (scores_flat == 0).float().mean().item()
            }
            
            layer_results[pred_name] = {
                'compute_time': compute_time,
                'pruning_quality': pruning_quality,
                'distribution': distribution_stats
            }
            
            # 输出关键指标
            f1_50 = pruning_quality[0.5]['f1_score']
            f1_70 = pruning_quality[0.7]['f1_score']
            
            print(f"{pred_name:>8}: F1@50%={f1_50:.3f}, F1@70%={f1_70:.3f}, "
                  f"动态范围={distribution_stats['dynamic_range']:.3f}, "
                  f"时间={compute_time:.4f}s")
        
        all_results[layer_name] = layer_results
    
    return all_results

def plot_performance_comparison(results):
    """绘制性能比较图"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. F1分数比较（50%稀疏度）
        layer_names = list(results.keys())
        predictors = ['Dolphin', 'Wanda', 'SNIP']
        
        f1_scores = []
        for pred in predictors:
            scores = [results[layer][pred]['pruning_quality'][0.5]['f1_score'] for layer in layer_names]
            f1_scores.append(scores)
        
        x = np.arange(len(layer_names))
        width = 0.25
        
        for i, (pred, scores) in enumerate(zip(predictors, f1_scores)):
            axes[0,0].bar(x + i*width, scores, width, label=pred)
        
        axes[0,0].set_xlabel('Layer Type')
        axes[0,0].set_ylabel('F1 Score @ 50% Sparsity')
        axes[0,0].set_title('Pruning Quality Comparison')
        axes[0,0].set_xticks(x + width)
        axes[0,0].set_xticklabels(layer_names)
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. 动态范围比较
        dynamic_ranges = []
        for pred in predictors:
            ranges = [results[layer][pred]['distribution']['dynamic_range'] for layer in layer_names]
            dynamic_ranges.append(ranges)
        
        for i, (pred, ranges) in enumerate(zip(predictors, dynamic_ranges)):
            axes[0,1].bar(x + i*width, ranges, width, label=pred)
        
        axes[0,1].set_xlabel('Layer Type')
        axes[0,1].set_ylabel('Dynamic Range')
        axes[0,1].set_title('Score Distribution Comparison')
        axes[0,1].set_xticks(x + width)
        axes[0,1].set_xticklabels(layer_names)
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 计算时间比较
        compute_times = []
        for pred in predictors:
            times = [results[layer][pred]['compute_time'] * 1000 for layer in layer_names]  # 转换为毫秒
            compute_times.append(times)
        
        for i, (pred, times) in enumerate(zip(predictors, compute_times)):
            axes[1,0].bar(x + i*width, times, width, label=pred)
        
        axes[1,0].set_xlabel('Layer Type')
        axes[1,0].set_ylabel('Compute Time (ms)')
        axes[1,0].set_title('Computational Efficiency')
        axes[1,0].set_xticks(x + width)
        axes[1,0].set_xticklabels(layer_names)
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 4. 不同稀疏度下的F1分数曲线
        sparsity_levels = [0.1, 0.3, 0.5, 0.7, 0.9]
        
        for pred in predictors:
            # 使用q_proj层的结果
            f1_curve = [results['q_proj'][pred]['pruning_quality'][s]['f1_score'] for s in sparsity_levels]
            axes[1,1].plot(sparsity_levels, f1_curve, marker='o', label=pred, linewidth=2)
        
        axes[1,1].set_xlabel('Sparsity Level')
        axes[1,1].set_ylabel('F1 Score')
        axes[1,1].set_title('F1 Score vs Sparsity (Q-Proj Layer)')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('predictor_performance_comparison.png', dpi=300, bbox_inches='tight')
        print("\n性能比较图已保存为 'predictor_performance_comparison.png'")
        
    except Exception as e:
        print(f"绘图失败: {e}")

def summary_report(results):
    """生成总结报告"""
    print("\n" + "="*60)
    print("总结报告")
    print("="*60)
    
    # 计算平均性能
    predictors = ['Dolphin', 'Wanda', 'SNIP']
    layer_types = list(results.keys())
    
    print("\n平均性能指标:")
    print("-" * 30)
    
    for pred in predictors:
        avg_f1_50 = np.mean([results[layer][pred]['pruning_quality'][0.5]['f1_score'] for layer in layer_types])
        avg_f1_70 = np.mean([results[layer][pred]['pruning_quality'][0.7]['f1_score'] for layer in layer_types])
        avg_dynamic_range = np.mean([results[layer][pred]['distribution']['dynamic_range'] for layer in layer_types])
        avg_time = np.mean([results[layer][pred]['compute_time'] for layer in layer_types])
        
        print(f"{pred:>8}:")
        print(f"  F1@50%: {avg_f1_50:.3f}")
        print(f"  F1@70%: {avg_f1_70:.3f}")
        print(f"  动态范围: {avg_dynamic_range:.3f}")
        print(f"  计算时间: {avg_time:.4f}s")
        print()
    
    # 推荐使用场景
    print("推荐使用场景:")
    print("-" * 20)
    print("• Dolphin: 需要高质量剪枝的复杂层（注意力层、关键FFN层）")
    print("• Wanda: 平衡性能和效率的通用场景")
    print("• SNIP: 需要激进剪枝的简单层（下投影层、输出层）")

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行综合评估
    results = comprehensive_evaluation()
    
    # 生成可视化
    plot_performance_comparison(results)
    
    # 生成总结报告
    summary_report(results)
    
    print("\n" + "="*60)
    print("评估完成!")
    print("="*60)
