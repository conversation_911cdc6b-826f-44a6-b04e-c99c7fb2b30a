#!/usr/bin/env python3
"""
测试优化后预测器性能的脚本

验证三个优化后的预测器：
1. Dolphin预测器 - 多特征融合
2. Wanda预测器 - 增强版本
3. SNIP预测器 - 连接敏感性增强
"""

import torch
import numpy as np
import time
from lib.predictors import PredictorRegistry

def generate_synthetic_data(batch_size=32, seq_len=128, in_features=512, out_features=256):
    """生成合成测试数据"""
    # 生成权重矩阵
    weight_matrix = torch.randn(out_features, in_features) * 0.1
    
    # 生成激活数据（模拟真实的激活分布）
    activations = torch.randn(in_features, seq_len * batch_size)
    
    # 添加一些特殊模式
    # 1. 一些通道有更高的方差
    high_var_channels = torch.randperm(in_features)[:in_features//4]
    activations[high_var_channels] *= 2.0
    
    # 2. 一些通道有偏度
    skewed_channels = torch.randperm(in_features)[:in_features//4]
    activations[skewed_channels] = torch.abs(activations[skewed_channels])
    
    # 3. 一些通道有更大的动态范围
    range_channels = torch.randperm(in_features)[:in_features//4]
    activations[range_channels] += torch.randint(-5, 6, (len(range_channels), 1)).float()
    
    return weight_matrix, activations

def compute_activation_stats(activations):
    """计算激活统计信息"""
    stats = {}
    
    # 基础统计
    stats['mean'] = torch.mean(activations, dim=1)
    stats['var'] = torch.var(activations, dim=1, unbiased=False)
    stats['l2_norm'] = torch.norm(activations, p=2, dim=1)
    stats['max_val'] = torch.max(activations, dim=1)[0]
    stats['min_val'] = torch.min(activations, dim=1)[0]
    
    # 高阶统计
    centered = activations - stats['mean'].unsqueeze(1)
    stats['skewness'] = torch.mean(centered ** 3, dim=1) / (stats['var'] ** 1.5 + 1e-8)
    
    # 简化的熵估计
    vars = stats['var']
    ranges = stats['max_val'] - stats['min_val']
    variance_entropy = 0.5 * torch.log(2 * 3.14159 * torch.e * (vars + 1e-12))
    range_entropy = torch.log(ranges + 1e-6)
    stats['entropy'] = 0.7 * variance_entropy + 0.3 * range_entropy
    stats['entropy'] = torch.clamp(stats['entropy'], min=0.0)
    
    return stats

def test_predictor_performance():
    """测试预测器性能"""
    print("测试优化后的预测器性能...")
    print("="*60)
    
    # 生成测试数据
    weight_matrix, activations = generate_synthetic_data()
    activation_stats = compute_activation_stats(activations)
    
    # 层信息
    layer_info = {
        'layer_id': 10,
        'layer_name': 'q_proj',
        'shape': weight_matrix.shape
    }
    
    # 测试三个预测器
    predictors = {
        'dolphin': PredictorRegistry.dolphin_predictor,
        'wanda': PredictorRegistry.wanda_predictor,
        'snip': PredictorRegistry.snip_predictor
    }
    
    results = {}
    
    for name, predictor in predictors.items():
        print(f"\n测试 {name.upper()} 预测器:")
        print("-" * 30)
        
        # 性能测试
        start_time = time.time()
        importance_scores = predictor(activation_stats, weight_matrix, layer_info)
        end_time = time.time()
        
        # 统计分析
        scores_flat = importance_scores.flatten()
        
        results[name] = {
            'time': end_time - start_time,
            'mean': torch.mean(scores_flat).item(),
            'std': torch.std(scores_flat).item(),
            'min': torch.min(scores_flat).item(),
            'max': torch.max(scores_flat).item(),
            'sparsity_10': (scores_flat < torch.quantile(scores_flat, 0.1)).float().mean().item(),
            'sparsity_50': (scores_flat < torch.quantile(scores_flat, 0.5)).float().mean().item(),
            'dynamic_range': (torch.max(scores_flat) - torch.min(scores_flat)).item(),
            'shape': importance_scores.shape
        }
        
        print(f"计算时间: {results[name]['time']:.4f}秒")
        print(f"分数统计: 均值={results[name]['mean']:.4f}, 标准差={results[name]['std']:.4f}")
        print(f"分数范围: [{results[name]['min']:.4f}, {results[name]['max']:.4f}]")
        print(f"动态范围: {results[name]['dynamic_range']:.4f}")
        print(f"输出形状: {results[name]['shape']}")
        
        # 检查数值稳定性
        if torch.any(torch.isnan(importance_scores)) or torch.any(torch.isinf(importance_scores)):
            print("⚠️  警告: 检测到NaN或Inf值!")
        else:
            print("✅ 数值稳定性: 正常")
    
    return results

def test_layer_adaptivity():
    """测试层级自适应性"""
    print("\n\n测试层级自适应性...")
    print("="*60)
    
    # 生成测试数据
    weight_matrix, activations = generate_synthetic_data()
    activation_stats = compute_activation_stats(activations)
    
    # 不同层类型
    layer_types = [
        ('q_proj', 'Query投影层'),
        ('k_proj', 'Key投影层'),
        ('v_proj', 'Value投影层'),
        ('o_proj', 'Output投影层'),
        ('gate_proj', '门控投影层'),
        ('up_proj', '上投影层'),
        ('down_proj', '下投影层')
    ]
    
    predictor = PredictorRegistry.dolphin_predictor
    
    print("Dolphin预测器在不同层类型下的表现:")
    print("-" * 50)
    
    for layer_name, description in layer_types:
        layer_info = {
            'layer_id': 10,
            'layer_name': layer_name,
            'shape': weight_matrix.shape
        }
        
        importance_scores = predictor(activation_stats, weight_matrix, layer_info)
        scores_flat = importance_scores.flatten()
        
        mean_score = torch.mean(scores_flat).item()
        std_score = torch.std(scores_flat).item()
        
        print(f"{description:>12} ({layer_name:>9}): 均值={mean_score:.4f}, 标准差={std_score:.4f}")

def test_robustness():
    """测试鲁棒性"""
    print("\n\n测试鲁棒性...")
    print("="*60)
    
    # 生成基础数据
    weight_matrix, activations = generate_synthetic_data()
    
    # 测试场景
    scenarios = [
        ("正常数据", activations),
        ("含噪声数据", activations + torch.randn_like(activations) * 0.1),
        ("极值数据", torch.clamp(activations, -10, 10)),
        ("稀疏数据", activations * (torch.rand_like(activations) > 0.5).float())
    ]
    
    layer_info = {'layer_id': 10, 'layer_name': 'q_proj'}
    predictor = PredictorRegistry.dolphin_predictor
    
    print("Dolphin预测器鲁棒性测试:")
    print("-" * 40)
    
    for scenario_name, test_activations in scenarios:
        try:
            activation_stats = compute_activation_stats(test_activations)
            importance_scores = predictor(activation_stats, weight_matrix, layer_info)
            
            # 检查输出质量
            scores_flat = importance_scores.flatten()
            has_nan = torch.any(torch.isnan(importance_scores))
            has_inf = torch.any(torch.isinf(importance_scores))
            dynamic_range = (torch.max(scores_flat) - torch.min(scores_flat)).item()
            
            status = "✅ 正常" if not (has_nan or has_inf) else "❌ 异常"
            print(f"{scenario_name:>12}: {status}, 动态范围={dynamic_range:.4f}")
            
        except Exception as e:
            print(f"{scenario_name:>12}: ❌ 错误 - {str(e)}")

def compare_with_baseline():
    """与基线方法比较"""
    print("\n\n与基线方法比较...")
    print("="*60)
    
    # 生成测试数据
    weight_matrix, activations = generate_synthetic_data()
    activation_stats = compute_activation_stats(activations)
    layer_info = {'layer_id': 10, 'layer_name': 'q_proj'}
    
    # 基线方法：简单的权重幅度
    def magnitude_baseline(activation_stats, weight_matrix, layer_info=None):
        return torch.abs(weight_matrix)
    
    # 基线方法：简单的随机
    def random_baseline(activation_stats, weight_matrix, layer_info=None):
        return torch.rand_like(weight_matrix)
    
    methods = {
        'Magnitude': magnitude_baseline,
        'Random': random_baseline,
        'Dolphin': PredictorRegistry.dolphin_predictor,
        'Wanda': PredictorRegistry.wanda_predictor,
        'SNIP': PredictorRegistry.snip_predictor
    }
    
    print("方法比较 (动态范围和分布特性):")
    print("-" * 50)
    
    for name, method in methods.items():
        importance_scores = method(activation_stats, weight_matrix, layer_info)
        scores_flat = importance_scores.flatten()
        
        dynamic_range = (torch.max(scores_flat) - torch.min(scores_flat)).item()
        entropy_approx = -torch.sum(torch.softmax(scores_flat, dim=0) * 
                                   torch.log_softmax(scores_flat, dim=0)).item()
        
        print(f"{name:>10}: 动态范围={dynamic_range:.4f}, 熵近似={entropy_approx:.4f}")

if __name__ == "__main__":
    # 设置随机种子确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行所有测试
    test_predictor_performance()
    test_layer_adaptivity()
    test_robustness()
    compare_with_baseline()
    
    print("\n" + "="*60)
    print("所有测试完成!")
    print("="*60)
